# FAAFO Career Platform

> **Find your path to career freedom through personalized assessments, financial planning, and community support.**

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?logo=typescript&logoColor=white)](https://typescriptlang.org/)
[![Next.js](https://img.shields.io/badge/Next.js-000000?logo=next.js&logoColor=white)](https://nextjs.org/)
[![Prisma](https://img.shields.io/badge/Prisma-2D3748?logo=prisma&logoColor=white)](https://prisma.io/)

## 🚀 Overview

FAAFO (Find Another Alternative, Find Other Options) Career Platform is a comprehensive web application designed to help professionals transition their careers with confidence. The platform provides personalized career assessments, financial planning tools, learning resource recommendations, and community support.

### ✨ Key Features

- **🎯 Personalized Career Assessment**: Multi-step assessment to identify career preferences and goals
- **💰 Freedom Fund Calculator**: Financial planning tool to calculate savings needed for career transitions
- **📚 Learning Resource Recommendations**: Curated learning materials based on career goals
- **📊 Progress Tracking**: Monitor learning progress and career development milestones
- **🌐 Community Forum**: Connect with other professionals on similar career journeys
- **🔐 Secure Authentication**: NextAuth.js integration with multiple providers
- **📱 Mobile Responsive**: Optimized for all devices
- **🌙 Dark Mode**: Built-in theme switching
- **⚡ Performance Optimized**: Caching, monitoring, and optimization features

## 🚀 Phase 1 Complete! ✅

**Latest Update**: Phase 1 development is now complete with advanced AI-powered features and comprehensive learning management!

### 🤖 New AI-Powered Features
- **Resume Analysis**: AI-powered resume optimization with Google Gemini
- **Career Recommendations**: Personalized career path suggestions based on assessments
- **Skills Gap Analysis**: Intelligent analysis of skill gaps with learning recommendations
- **Interview Preparation**: AI-generated interview questions and preparation materials

### 📚 Advanced Learning Management
- **Structured Learning Paths**: Step-by-step learning journeys with progress tracking
- **Skill Progression**: Automatic skill level tracking and advancement
- **Learning Analytics**: Comprehensive analytics and insights dashboard
- **Progress Tracking**: Real-time progress updates and completion metrics

### ⚡ Performance & Optimization
- **Database Optimization**: 50+ performance indexes and query optimization
- **Intelligent Caching**: Redis integration with memory fallback
- **API Documentation**: Interactive Swagger UI with complete endpoint coverage
- **Health Monitoring**: Real-time service health checks and performance metrics

### 📖 Getting Started with Phase 1
- **Setup Guide**: See [PHASE1_SETUP_GUIDE.md](PHASE1_SETUP_GUIDE.md) for detailed setup instructions
- **Implementation Details**: See [PHASE1_IMPLEMENTATION_COMPLETE.md](PHASE1_IMPLEMENTATION_COMPLETE.md) for technical details
- **API Documentation**: Visit `/api-docs` for interactive API documentation
- **Test Suite**: Run `npx tsx scripts/test-phase1-features.ts` to validate setup

## 🛠️ Tech Stack

### Frontend
- **Framework**: Next.js 15 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Lucide React Icons
- **State Management**: React Hooks + Context
- **Theme**: next-themes

### Backend
- **API**: Next.js API Routes
- **Database**: SQLite (development) / PostgreSQL (production)
- **ORM**: Prisma
- **Authentication**: NextAuth.js
- **Validation**: Zod
- **Security**: Rate limiting, CSRF protection, input sanitization

### AI & Optimization (Phase 1)
- **AI Integration**: Google Gemini API for intelligent features
- **Caching**: Redis with memory fallback for performance
- **Database Optimization**: Performance indexes and query monitoring
- **API Documentation**: OpenAPI 3.0 with Swagger UI
- **File Processing**: PDF/DOCX parsing for resume analysis
- **Performance Monitoring**: Real-time metrics and health checks

### Development & Testing
- **Testing**: Jest + React Testing Library
- **Linting**: ESLint
- **Type Checking**: TypeScript
- **Package Manager**: npm

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- npm or yarn
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/dm601990/faafo.git
   cd faafo/faafo-career-platform
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```

   Edit `.env.local` with your configuration:
   ```env
   # Database
   DATABASE_URL="file:./dev.db"

   # NextAuth
   NEXTAUTH_SECRET="your-secret-key-here"
   NEXTAUTH_URL="http://localhost:3000"

   # AI Configuration (Phase 1)
   GOOGLE_GEMINI_API_KEY="your-google-gemini-api-key"
   GEMINI_MODEL="gemini-pro"
   AI_CACHE_TTL=3600

   # Redis Configuration (Optional - uses memory cache if not provided)
   REDIS_URL="redis://localhost:6379"
   REDIS_TTL=1800

   # Database Optimization
   DATABASE_CONNECTION_POOL_SIZE=20
   DATABASE_QUERY_TIMEOUT=5000

   # Admin Configuration
   ADMIN_EMAIL="<EMAIL>"

   # Email (optional)
   EMAIL_SERVER_HOST="smtp.gmail.com"
   EMAIL_SERVER_PORT=587
   EMAIL_SERVER_USER="<EMAIL>"
   EMAIL_SERVER_PASSWORD="your-app-password"
   EMAIL_FROM="<EMAIL>"
   ```

4. **Set up the database**
   ```bash
   npx prisma generate
   npx prisma db push
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
faafo-career-platform/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── api/               # API routes
│   │   ├── dashboard/         # Dashboard pages
│   │   ├── globals.css        # Global styles
│   │   └── layout.tsx         # Root layout
│   ├── components/            # React components
│   │   ├── layout/           # Layout components
│   │   └── ui/               # UI components
│   ├── lib/                   # Utility libraries
│   │   ├── services/         # Business logic services
│   │   ├── auth.ts           # Authentication config
│   │   ├── prisma.ts         # Database client
│   │   ├── validation.ts     # Input validation schemas
│   │   ├── errorHandler.ts   # Error handling utilities
│   │   ├── rateLimit.ts      # Rate limiting utilities
│   │   ├── cache.ts          # Caching utilities
│   │   └── monitoring.ts     # Performance monitoring
│   └── types/                 # TypeScript type definitions
├── prisma/
│   ├── schema.prisma         # Database schema
│   └── migrations/           # Database migrations
├── docs/                     # Documentation
├── __tests__/                # Test files
├── public/                   # Static assets
└── package.json
```

## 🧪 Testing

Run the test suite:

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

## 🔧 Development

### Available Scripts

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # Run TypeScript checks

# Database
npx prisma studio    # Open Prisma Studio
npx prisma generate  # Generate Prisma client
npx prisma db push   # Push schema changes
npx prisma migrate   # Run migrations

# Testing
npm test             # Run tests
npm run test:watch   # Run tests in watch mode
```

### Code Quality

The project includes several tools to maintain code quality:

- **ESLint**: Linting and code style enforcement
- **TypeScript**: Static type checking
- **Prettier**: Code formatting (configured in ESLint)
- **Husky**: Git hooks for pre-commit checks
- **Jest**: Unit and integration testing

## 📊 Features Deep Dive

### Career Assessment
- Multi-step questionnaire covering career preferences, skills, and goals
- Dynamic question flow based on previous answers
- Progress saving and resume functionality
- Personalized career path recommendations

### Freedom Fund Calculator
- Calculate savings needed for career transition
- Factor in monthly expenses and desired coverage period
- Track progress toward financial goals
- Visual progress indicators

### Learning Resources
- Curated learning materials for different career paths
- Filter by skill level, type, and category
- Progress tracking for each resource
- User ratings and reviews

### Progress Tracking
- Monitor learning milestones
- Track assessment completion
- Visualize career development journey
- Achievement badges and rewards

## 🔒 Security Features

- **Authentication**: Secure session-based authentication with NextAuth.js
- **Authorization**: Role-based access control
- **Rate Limiting**: API endpoint protection against abuse
- **CSRF Protection**: Cross-site request forgery prevention
- **Input Validation**: Comprehensive input sanitization and validation
- **Security Headers**: HTTP security headers implementation
- **Password Security**: Strong password requirements and secure hashing

## 🚀 Deployment

### Environment Setup

1. **Production Environment Variables**
   ```env
   NODE_ENV=production
   DATABASE_URL="your-production-database-url"
   NEXTAUTH_SECRET="your-production-secret"
   NEXTAUTH_URL="https://your-domain.com"
   ```

2. **Database Migration**
   ```bash
   npx prisma migrate deploy
   ```

### Deployment Platforms

#### Vercel (Recommended)
1. Connect your GitHub repository to Vercel
2. Configure environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

#### Docker
```dockerfile
# Dockerfile included in project root
docker build -t faafo-career-platform .
docker run -p 3000:3000 faafo-career-platform
```

#### Traditional Hosting
```bash
npm run build
npm start
```

## 📈 Performance & Monitoring

The platform includes built-in performance monitoring:

- **Core Web Vitals**: LCP, FID, CLS tracking
- **API Performance**: Response time monitoring
- **Error Tracking**: Automatic error capture and reporting
- **User Analytics**: User interaction tracking
- **Caching**: Multi-layer caching strategy

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Workflow

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass: `npm test`
6. Commit your changes: `git commit -m 'Add amazing feature'`
7. Push to the branch: `git push origin feature/amazing-feature`
8. Open a Pull Request

## 📝 API Documentation

Comprehensive API documentation is available at [docs/API.md](docs/API.md).

### Quick API Reference

```bash
# Authentication
POST /api/signup              # Create account
GET  /api/csrf-token         # Get CSRF token

# Assessment
GET  /api/assessment         # Get user assessment
POST /api/assessment         # Save assessment progress
PUT  /api/assessment         # Submit final assessment

# Learning
GET  /api/learning-resources # Get learning resources
GET  /api/learning-progress  # Get user progress
POST /api/learning-progress  # Update progress

# Career Planning
GET  /api/career-paths       # Get career paths
GET  /api/personalized-resources # Get recommendations
GET  /api/freedom-fund       # Get freedom fund data
POST /api/freedom-fund       # Update freedom fund
```

## 🐛 Troubleshooting

### Common Issues

**Database Connection Issues**
```bash
# Reset database
npx prisma db push --force-reset
npx prisma generate
```

**Build Errors**
```bash
# Clear Next.js cache
rm -rf .next
npm run build
```

**Type Errors**
```bash
# Regenerate Prisma client
npx prisma generate
npm run type-check
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) for the amazing React framework
- [Prisma](https://prisma.io/) for the excellent database toolkit
- [NextAuth.js](https://next-auth.js.org/) for authentication
- [Tailwind CSS](https://tailwindcss.com/) for utility-first CSS
- [Lucide](https://lucide.dev/) for beautiful icons

## 📞 Support

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/faafo)
- 📖 Documentation: [docs.faafo.com](https://docs.faafo.com)
- 🐛 Issues: [GitHub Issues](https://github.com/dm601990/faafo/issues)

---

**Made with ❤️ by the FAAFO Team**

<!-- Dummy commit to trigger Vercel redeployment -->
